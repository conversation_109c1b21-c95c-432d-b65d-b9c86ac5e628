
Ben Türkiye'de spor salonu yönetim sistemi geliştiren bir yazılımcıyım. Şu anda sistemim 1 spor salonunda canlı olarak çalışıyor ve sorunsuz çalışıyor. Ancak hedefim bu sistemi 1000+ spor salonuna satmak ve tek bir veritabanı üzerinde çok kiracılı (multi-tenant) bir altyapı kurmak.

MEVCUT DURUM:
- 1 spor salonu, yaklaşık 100 üye
- .NET Core Web API + Angular frontend
- SQL Server veritabanı
- Entity Framework Core
- Autofac Dependency Injection
- Multi-tenant altyapı mevcut (CompanyID bazlı)

HEDEF:
- 1000+ spor salonu
- 100,000+ toplam üye
- Tek veritabanı
- Aynı performans seviyesi
- 7/24 kesintisiz hizmet

ENDIŞE ETTİĞİM KONULAR:

1. DEPENDENCY INJECTION LIFETIME MANAGEMENT:
   - AutofacBusinessModule'de tüm DAL sınıfları SingleInstance() olarak kayıtlı
   - Bu memory leak'e neden olabilir mi?
   - 100,000 kullanıcıda sistem çöker mi?

2. DbContext LIFETIME YÖNETİMİ:
   - Her DAL metodunda "using (var context = new GymContext())" kullanılıyor
   - Connection pool tükenir mi?
   - DbContext lifetime ile DI uyumlu mu?

3. PERFORMANS VE SCALABILITY:
   - Mevcut sistem 1000 salon + 100,000 üyeyi kaldırır mı?
   - Connection pooling optimizasyonu gerekli mi?
   - Database indexleri yeterli mi?

4. MEMORY MANAGEMENT:
   - Memory leak riski var mı?
   - Garbage collection sorunları olur mu?
   - Cache stratejisi yeterli mi?

TALEBİM:
Lütfen mevcut sistemimi analiz et ve:
1. Hangi kısımlar scalability için sorunlu?
2. Hangi değişiklikler yapılmalı?
3. Hangi sırayla uygulanmalı?
4. Risk analizi nedir?

Sistemimi inceleyip detaylı bir rapor çıkarmanı ve nereleri nasıl değiştirmem gerektiğini söylemeni istiyorum.

NOT: Lütfen sadece analiz yap, kod değişikliği yapma. Önce durumu anlayalım, sonra adım adım ilerleyelim.

Teşekkürler.
